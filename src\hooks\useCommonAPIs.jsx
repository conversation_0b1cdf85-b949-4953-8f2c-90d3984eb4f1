import { useDispatch, useSelector } from 'react-redux';
import { DEIDENTIFIED, CURRENT_DATE } from '@/containers/commonConstants';
import { getOrganizationAndWidgetId, getParamFromUrl } from '@/containers/commonUtility';
import { checkExistingClientIndex } from '@/redux/actions/checkExistingClientIndex';
import { checkExistingConnectionIndex } from '@/redux/actions/checkExistingConnectionIndex';
import { createConnectionAtClientIndex } from '@/redux/actions/createConnectionAtClientIndex';
import { createConnectionAtConnectionIndex } from '@/redux/actions/createConnectionAtConnectionIndex';
import { searchIndividualAtClientIndex } from '@/redux/actions/searchIndividualAtClientIndex';
import { updateConnectionAtClientIndex } from '@/redux/actions/updateConnectionAtClientIndex';
import { useSession } from 'next-auth/react';
import { fetchConsentAgreement } from '@/redux/actions/getConsentAgreement';
import { getIndividualDataRequest } from '@/redux/actions/getIndividualData';

export const useCommonAPIs = (props) => {
  const dispatch = useDispatch();
  const [organizationId] = getOrganizationAndWidgetId();

  const { data: session } = useSession();

  const { consentAgreementSuccessData } = useSelector((state) => state.consentAgreementReducer);
  const { createConnectionAtClientIndexSuccessData } = useSelector(
    (state) => state.createConnectionAtClientIndexReducer,
  );
  const { searchIndividualAtClientIndexSuccessData } = useSelector(
    (state) => state.searchIndividualAtClientIndexReducer,
  );
  const { checkExistingConnectionIndexSuccessData } =
    useSelector((state) => state.checkExistingConnectionIndexReducer) || {};
  const { checkExistingClientIndexSuccessData } = useSelector((state) => state.checkExistingClientIndexReducer) || {};

  const getClientId = () => {
    const client_id = searchIndividualAtClientIndexSuccessData?.data?.clientId;
    const clientId =
      (searchIndividualAtClientIndexSuccessData?.data?.found && client_id) ||
      checkExistingConnectionIndexSuccessData?.clientId;
    return clientId;
  };

  const getSchedulerClientId = () => {
    const clientId = searchIndividualAtClientIndexSuccessData?.data?.identifiers?.find(
      (identifier) => identifier.type === 'SCHEDULER_CLIENT_ID',
    )?.value;

    return clientId;
  };

  const getPatientId = () => {
    const patient_id = searchIndividualAtClientIndexSuccessData?.data?.identifiers?.find(
      (identifier) => identifier.type === 'CDR_PATIENT_ID',
    )?.value;
    const existingPatientId = checkExistingClientIndexSuccessData?.clientIdentifiers?.find(
      (identifier) => identifier.type === 'CDR_PATIENT_ID',
    )?.value;
    const createdPatientId = createConnectionAtClientIndexSuccessData?.patientId;

    const patientId =
      existingPatientId || createdPatientId || (searchIndividualAtClientIndexSuccessData?.data?.found && patient_id);
    return patientId;
  };

  const createSearchClientIndexBody = (demographic, selfRegistration) => {
    const pathname = window.location.pathname;
    const isBookingWidget = pathname.includes('bookingWidget');
    const clientId = getClientId();

    const sessionCambianId = session?.user?.cambianId;

    const identifiers = [];
    if (sessionCambianId) {
      identifiers.push({
        type: 'CAMBIAN_ID',
        value: sessionCambianId,
      });
    }
    const appointmentId = getParamFromUrl('appointmentId');

    let resyncIdTypes = [];
    if (isBookingWidget) {
      if (appointmentId) {
        resyncIdTypes = [
          {
            idType: 'SCHEDULER_CLIENT_ID',
            appointmentId: appointmentId,
          },
        ];
      } else {
        resyncIdTypes = [
          {
            idType: 'SCHEDULER_CLIENT_ID',
          },
        ];
      }
    }

    const searchBody = {
      client: {
        dateOfBirth: demographic.individuals[0].dateOfBirth,
        firstName: demographic.individuals[0].firstName,
        lastName: demographic.individuals[0].lastName,
        middleName: demographic.individuals[0].middleName,
        gender: demographic.individuals[0].gender,
        addresses: demographic.contact.addresses,
        emailAddresses: demographic.contact.emailAddresses,
        phoneNumbers: demographic.contact.phoneNumbers,
        dataSource: 'WIDGET',
        consentAgreementDate: consentAgreementSuccessData ? CURRENT_DATE : '',
        preferredContactMechanism: demographic.preferredContactMethod,
        subscribeToNotifications: demographic.subscribeToNotifications,
        clientGroups: [],
        healthCareIds: demographic.individuals[0].healthCareIds,
        clientId: clientId || null,
        identifiers: identifiers,
      },
      selfRegistration,
      resyncIdTypes: resyncIdTypes,
    };

    return searchBody;
  };

  const checkConnectionAtConnectionIndex = () => {
    //const organizationId = '6ae77cdb-a71c-4753-a8d4-b8f6fa632f53';
    const individualId = session?.user?.cambianId;

    dispatch(
      checkExistingConnectionIndex({
        headers: { Authorization: session?.user?.idToken },
        organizationId,
        individualId,
        includeSharedAttributes: true,
      }),
    );
  };

  const checkConnectionAtClientIndex = () => {
    //const organizationId = '6ae77cdb-a71c-4753-a8d4-b8f6fa632f53';
    const individualId = checkExistingConnectionIndexSuccessData?.aliasId;

    dispatch(
      checkExistingClientIndex({
        organizationId,
        individualId,
      }),
    );
  };

  const makeConnectionAtClientIndex = ({ identification, demographic }) => {
    const connectionData =
      identification === DEIDENTIFIED
        ? {
            clientId: null,
            dataSource: 'WIDGET',
            consentAgreementDate: CURRENT_DATE, // * consentAgreementId replaced with consentAgreementDate
          }
        : {
            firstName: session?.user?.firstName || demographic?.individuals[0]?.firstName || '',
            lastName: session?.user?.lastName || demographic?.individuals[0]?.lastName || '',
            middleName: session?.user?.middleName || demographic?.individuals[0]?.middleName || '',
            emailAddresses: demographic?.contact?.emailAddresses
              ? demographic?.contact?.emailAddresses
              : session?.user?.email
                ? [{ emailAddress: session?.user?.email, primary: true }]
                : [],
            addresses: demographic?.contact?.addresses || [],
            preferredContactMechanism: demographic?.preferredContactMethod || 'Email',
            subscribeToNotifications: demographic?.subscribeToNotifications || true,
            dateOfBirth: demographic?.individuals[0]?.dateOfBirth || null,
            gender: demographic?.individuals[0]?.gender || null,
            cambianId: session?.user?.cambianId,
            clientId: session?.user?.cambianId,
            dataSource: 'WIDGET',
            clientGroups: [],
            consentAgreementDate: CURRENT_DATE, // * consentAgreementId replaced with consentAgreementDate
            phoneNumbers: demographic?.contact?.phoneNumbers
              ? demographic?.contact?.phoneNumbers
              : session?.user?.phone
                ? [{ phoneNumber: session?.user?.phone, primary: true }]
                : [],
            healthCareIds: demographic?.individuals[0]?.healthCareIds ? demographic?.individuals[0]?.healthCareIds : [],
          };

    dispatch(
      createConnectionAtClientIndex({
        organizationId,
        connectionData,
      }),
    );
  };

  const makeConnectionAtConnectionIndex = (clientId) => {
    // const organizationId = '6ae77cdb-a71c-4753-a8d4-b8f6fa632f53';
    const connectionData = {
      individualId: session?.user?.cambianId,
      type: 'ORGANIZATION',
      connectionEntityId: organizationId,
      clientId: clientId,
      consentDocumentArtifactId: organizationId, // * have been told by Nikhil that consentAgreementId would be same as organizationId
    };

    dispatch(
      createConnectionAtConnectionIndex({
        headers: { Authorization: session?.user?.idToken },
        organizationId,
        connectionData,
      }),
    );
  };

  const fetchUpdateConnectionAtClientIndex = (connectionData) => {
    // const organizationId = '6ae77cdb-a71c-4753-a8d4-b8f6fa632f53';
    // const individualId = '09d22340-1ef0-4b48-86a6-15192a389f6c';
    const individualId = getClientId();

    dispatch(
      updateConnectionAtClientIndex({
        organizationId,
        individualId,
        connectionData: { ...connectionData, clientId: individualId },
      }),
    );
  };

  const fetchSearchIndividualAtClientIndex = (demographics, selfRegistration) => {
    const searchBody = createSearchClientIndexBody(demographics, selfRegistration);

    dispatch(
      searchIndividualAtClientIndex({
        organizationId,
        searchBody,
      }),
    );
  };

  const fetchConsentAgreementDocument = () => {
    dispatch(
      fetchConsentAgreement({
        organizationId, // * have been told by Nikhil that consentAgreementId would be same as organizationId
      }),
    );
  };

  const fetchIndividualData = (individualId) => {
    dispatch(
      getIndividualDataRequest({
        individualId: individualId,
      }),
    );
  };

  const handlePostSignInClientIndexUpdate = () => {
    // This function handles client index and connection index updates after SignIn/SignUp
    // It ensures consistency across different entry points (demographic, save for later, questionnaire save)

    if (!session?.user?.cambianId) {
      console.log('No cambianId found in session, skipping client index update');
      return;
    }

    // First, check if connection exists in connection index
    checkConnectionAtConnectionIndex();
  };

  return {
    checkConnectionAtClientIndex,
    checkConnectionAtConnectionIndex,
    makeConnectionAtClientIndex,
    makeConnectionAtConnectionIndex,
    fetchUpdateConnectionAtClientIndex,
    fetchSearchIndividualAtClientIndex,
    fetchConsentAgreementDocument,
    getClientId,
    getSchedulerClientId,
    getPatientId,
    fetchIndividualData,
    handlePostSignInClientIndexUpdate,
  };
};
