# This is for the pcn-test account

NEXT_PUBLIC_BASE_URL = https://widget.test.alberta-pcn.ca

# * APIs variables
NEXT_PUBLIC_WIDGET_SERVICES_BASE_URL=https://test.alberta-pcn.ca/widget-config

# Schedular Booking API Gateway
NEXT_PUBLIC_BOOKING_SERVICES_BASE_URL=https://test.alberta-pcn.ca/scheduler-booking

# Artifact repository
# Private Artifact Repository
NEXT_PUBLIC_ARTIFACT_PRIVATE_REPOSITORY_BASE_URL=https://test.alberta-pcn.ca/org-artifact-repo

# Public Artifact Repository
NEXT_PUBLIC_ARTIFACT_PUBLIC_REPOSITORY_BASE_URL=https://test.cambianservices.ca/net-artifact-repo

# CDRs
NEXT_PUBLIC_INDIVIDUAL_CDR_BASE_URL=https://70e4ltesad.execute-api.ca-central-1.amazonaws.com/test

NEXT_PUBLIC_ORGANIZATION_CDR_BASE_URL=https://wh54glk4na.execute-api.ca-central-1.amazonaws.com/test

# Indexes
NEXT_PUBLIC_CONNECTION_INDEX_BASE_URL=https://tt835tu6n3.execute-api.ca-central-1.amazonaws.com/test

NEXT_PUBLIC_CLIENT_INDEX_BASE_URL=https://48x68y0cpk.execute-api.ca-central-1.amazonaws.com/test

NEXT_PUBLIC_ORGANIZATION_MESSAGING_SERVICE_BASE_URL=https://test.alberta-pcn.ca/org-messaging

NEXT_PUBLIC_ORGANIZATION_INDIVIDUAL_SERVICE_BASE_URL=https://fq53ljixj1.execute-api.ca-central-1.amazonaws.com/test

# Are they necessary?
NEXT_PUBLIC_PRACTITIONER_INDEX_IDENTIFIER=3fd7szhb9f
NEXT_PUBLIC_PRACTITIONER_INDEX_STAGE=test

NEXT_PUBLIC_LOCATION_INDEX_IDENTIFIER=10zk9zdg9f
NEXT_PUBLIC_LOCATION_INDEX_STAGE=test

# Doc Gen
NEXT_PUBLIC_PDF_GENERATION_SERVICE_BASE_URL=https://test.alberta-pcn.ca/org-doc-gen

NEXT_PUBLIC_APPSCOOP_WIDGET_SERVICES_REGION=ca-central-1
NEXT_PUBLIC_CAMBIAN_SERVICES_REGION=ca-central-1

NEXT_PUBLIC_AWS_SERVICES_ENVIRONMENT=test

# Allow access to services in Org Env
COGNITO_ORG_MACHINE_USER_POOL_ID=ca-central-1_rZJUkfjVd
COGNITO_ORG_MACHINE_APP_CLIENT_ID=4ala6sdke5o9557dfckl06het5
COGNITO_ORG_MACHINE_USERNAME=69f927c0b80a6536c85646d8b967c178

# Allow access to services in Network Env
COGNITO_NETWORK_MACHINE_USER_POOL_ID=ca-central-1_VatJM2rrP
COGNITO_NETWORK_MACHINE_APP_CLIENT_ID=5cbehubvbmjormjjbci3cu6nd9
COGNITO_NETWORK_MACHINE_USERNAME=b5ff8e7d274dd00c6fa96d1486545307

# Allow access to services in Individual Env
COGNITO_INDIVIDUAL_MACHINE_USER_POOL_ID=ca-central-1_kQFbifOYL
COGNITO_INDIVIDUAL_MACHINE_APP_CLIENT_ID=1u4ib1n2memof9qcpfmhhi13ot
COGNITO_INDIVIDUAL_MACHINE_USERNAME=11d302c3ff014aca8873fd9030817291

# Cognito User auth variables
COGNITO_INDIVIDUAL_HUMAN_REGION=ca-central-1
COGNITO_INDIVIUDAL_HUMAN_USER_POOL_ID=ca-central-1_t5XYRZzvT
COGNITO_INDIVIUDAL_HUMAN_APP_CLIENT_ID=1o6f912kklrpao3i289a2qbum

NEXTAUTH_URL=$NEXT_PUBLIC_BASE_URL
