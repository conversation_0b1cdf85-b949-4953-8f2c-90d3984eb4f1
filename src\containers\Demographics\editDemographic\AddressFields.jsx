import React, { useEffect, useState } from 'react';
import { Grid, Box, FormControl, TextField, Button, IconButton, Stack } from '@mui/material';
import { Add, Star, StarOutline, Close } from '@mui/icons-material';
import BorderedSection from '@/components/BorderedSection';
import { CambianTooltip } from '@/components';

export const AddressFields = ({
  addresses,
  setAddresses,
  validationData,
  isClientSummary,
  t,
  item,
  demographicFields,
  handleContactChange,
}) => {
  const [currentAddresses, setCurrentAddresses] = useState(
    addresses.length
      ? addresses
      : [{ address1: '', address2: '', city: '', province: '', country: '', postalCode: '', primary: true }],
  );
  var primaryAddressIndex = addresses.length ? addresses.findIndex((address) => address.primary === true) : 0;

  useEffect(() => {
    setCurrentAddresses(
      addresses.length
        ? addresses
        : [{ address1: '', address2: '', city: '', province: '', country: '', postalCode: '', primary: true }],
    );
    primaryAddressIndex = addresses.length ? addresses.findIndex((address) => address.primary === true) : 0;
  }, [addresses]);

  const handleFieldChange = (index, field, value) => {
    const updatedAddresses = [...currentAddresses];
    updatedAddresses[index][field] = value;

    setCurrentAddresses(updatedAddresses);
    setAddresses(updatedAddresses);
    demographicFields.contact.addresses = updatedAddresses;
    handleContactChange(updatedAddresses, 'addresses', item.isMandatory);
  };

  const handleAddAddress = () => {
    const newAddress = {
      address1: '',
      address2: '',
      city: '',
      province: '',
      country: '',
      postalCode: '',
      primary: currentAddresses.length === 0,
    };

    const updatedAddresses = [...currentAddresses, newAddress];
    setCurrentAddresses(updatedAddresses);
    setAddresses(updatedAddresses);
    demographicFields.contact.addresses = updatedAddresses;
    handleContactChange(updatedAddresses, 'addresses', item.isMandatory);
  };

  const handleSetPrimaryAddress = (index) => {
    const updatedAddresses = currentAddresses.map((address, i) => ({
      ...address,
      primary: i === index,
    }));

    setCurrentAddresses(updatedAddresses);
    setAddresses(updatedAddresses);
    demographicFields.contact.addresses = updatedAddresses;
    handleContactChange(updatedAddresses, 'addresses', item.isMandatory);
  };

  const handleDeleteAddress = (index) => {
    const updatedAddresses = currentAddresses.filter((_, i) => i !== index);
    if (!updatedAddresses.some((address) => address.primary) && updatedAddresses.length > 0) {
      updatedAddresses[0].primary = true;
    }

    setCurrentAddresses(updatedAddresses);
    setAddresses(updatedAddresses);
    demographicFields.contact.addresses = updatedAddresses;
    if (validationData?.contact?.addresses) {
      validationData.contact.addresses.splice(index, 1);
    }
    handleContactChange(updatedAddresses, 'addresses', item.isMandatory);
  };

  const handleSingleAddressChange = (field, value) => {
    const updatedAddress = {
      ...demographicFields.contact.addresses[primaryAddressIndex],
      [field]: value,
      primary: true,
    };
    demographicFields.contact.addresses[primaryAddressIndex] = updatedAddress;
    setAddresses([updatedAddress]);
    handleContactChange([updatedAddress], 'addresses', item.isMandatory);
  };

  if (!item.allowMultiple) {
    return (
      <Grid item xs={12} sm={10} md={6} lg={5} sx={{ mt: 2, pl: { xs: 1, sm: 3 }, pr: 1 }}>
        <BorderedSection title={`${item.display}${item.isMandatory ? ' *' : ''}`}>
          <Box sx={{ mt: 2 }}>
            <Grid container spacing={2}>
              <Grid item xs={6}>
                <FormControl fullWidth variant="outlined">
                  <TextField
                    value={demographicFields?.contact?.addresses[primaryAddressIndex]?.address1 ?? ''}
                    onChange={(e) => handleSingleAddressChange('address1', e.target.value)}
                    label={t('addressLine1Placeholder')}
                    size="small"
                    inputProps={{
                      readOnly: isClientSummary,
                    }}
                    required
                    error={!!validationData?.contact?.addresses[primaryAddressIndex]?.address1}
                    helperText={validationData?.contact?.addresses[primaryAddressIndex]?.address1}
                  />
                </FormControl>
              </Grid>
              <Grid item xs={6}>
                <FormControl fullWidth variant="outlined">
                  <TextField
                    value={demographicFields?.contact?.addresses[primaryAddressIndex]?.address2 ?? ''}
                    onChange={(e) => handleSingleAddressChange('address2', e.target.value)}
                    label={t('addressLine2Placeholder')}
                    size="small"
                    inputProps={{
                      readOnly: isClientSummary,
                    }}
                  />
                </FormControl>
              </Grid>
              <Grid item xs={6}>
                <FormControl fullWidth variant="outlined">
                  <TextField
                    value={demographicFields?.contact?.addresses[primaryAddressIndex]?.city ?? ''}
                    onChange={(e) => handleSingleAddressChange('city', e.target.value)}
                    label={t('cityPlaceholder')}
                    size="small"
                    inputProps={{
                      readOnly: isClientSummary,
                    }}
                    required
                    error={!!validationData?.contact?.addresses[primaryAddressIndex]?.city}
                    helperText={validationData?.contact?.addresses[primaryAddressIndex]?.city}
                  />
                </FormControl>
              </Grid>
              <Grid item xs={6}>
                <FormControl fullWidth variant="outlined">
                  <TextField
                    value={demographicFields?.contact?.addresses[primaryAddressIndex]?.province ?? ''}
                    onChange={(e) => handleSingleAddressChange('province', e.target.value)}
                    label={t('provincePlaceholder')}
                    size="small"
                    inputProps={{
                      readOnly: isClientSummary,
                    }}
                    required
                    error={!!validationData?.contact?.addresses[primaryAddressIndex]?.province}
                    helperText={validationData?.contact?.addresses[primaryAddressIndex]?.province}
                  />
                </FormControl>
              </Grid>
              <Grid item xs={6}>
                <FormControl fullWidth variant="outlined">
                  <TextField
                    value={demographicFields?.contact?.addresses[primaryAddressIndex]?.country ?? ''}
                    onChange={(e) => handleSingleAddressChange('country', e.target.value)}
                    label={t('countryPlaceholder')}
                    size="small"
                    inputProps={{
                      readOnly: isClientSummary,
                    }}
                    required
                    error={!!validationData?.contact?.addresses[primaryAddressIndex]?.country}
                    helperText={validationData?.contact?.addresses[primaryAddressIndex]?.country}
                  />
                </FormControl>
              </Grid>
              <Grid item xs={6}>
                <FormControl fullWidth variant="outlined">
                  <TextField
                    value={demographicFields?.contact?.addresses[primaryAddressIndex]?.postalCode ?? ''}
                    onChange={(e) => handleSingleAddressChange('postalCode', e.target.value)}
                    label={t('postalCodePlaceholder')}
                    size="small"
                    inputProps={{
                      readOnly: isClientSummary,
                    }}
                    required
                    error={!!validationData?.contact?.addresses[primaryAddressIndex]?.postalCode}
                    helperText={validationData?.contact?.addresses[primaryAddressIndex]?.postalCode}
                  />
                </FormControl>
              </Grid>
            </Grid>
          </Box>
        </BorderedSection>
      </Grid>
    );
  }
  return (
    <Grid item xs={12} sm={10} md={6} lg={5} sx={{ mt: 1, pl: { xs: 1, sm: 3 }, pr: 1 }}>
      <BorderedSection title={`${item.display}${item.isMandatory ? ' *' : ''}`}>
        {currentAddresses.map((address, index) => (
          <Box key={index} sx={{ position: 'relative', mt: 2 }}>
            <Box sx={{ border: '1px solid lightgray', borderRadius: '4px', p: 2 }}>
              <Grid container spacing={2}>
                <Grid item xs={5}>
                  <FormControl fullWidth variant="outlined">
                    <TextField
                      value={address.address1}
                      onChange={(e) => handleFieldChange(index, 'address1', e.target.value)}
                      label={t('Address Line 1')}
                      size="small"
                      required
                      inputProps={{ readOnly: isClientSummary }}
                      error={!!validationData?.contact?.addresses[index]?.address1}
                      helperText={validationData?.contact?.addresses[index]?.address1}
                    />
                  </FormControl>
                </Grid>
                <Grid item xs={5}>
                  <FormControl fullWidth variant="outlined">
                    <TextField
                      value={address.address2}
                      onChange={(e) => handleFieldChange(index, 'address2', e.target.value)}
                      label={t('Address Line 2')}
                      size="small"
                      inputProps={{ readOnly: isClientSummary }}
                      error={!!validationData?.contact?.addresses[index]?.address2}
                      helperText={validationData?.contact?.addresses[index]?.address2}
                    />
                  </FormControl>
                </Grid>
                <Grid item xs={2}>
                  <Box
                    sx={{
                      display: 'flex',
                      justifyContent: 'flex-end',
                      alignItems: 'center',
                      height: '40px',
                      position: 'relative',
                      top: '0',
                      zIndex: 1,
                    }}
                  >
                    {address.primary ? (
                      <CambianTooltip title={t('Primary')}>
                        <Star color="primary" sx={{ fontSize: '20px' }} />
                      </CambianTooltip>
                    ) : (
                      <CambianTooltip title={t('Set as Primary')}>
                        <StarOutline
                          color="primary"
                          sx={{ cursor: 'pointer', fontSize: '20px' }}
                          onClick={() => handleSetPrimaryAddress(index)}
                        />
                      </CambianTooltip>
                    )}
                    {currentAddresses?.length > 1 && (
                      <CambianTooltip title={t('Delete Address')}>
                        <Close
                          color="gray"
                          sx={{ cursor: 'pointer', ml: 1, fontSize: '20px' }}
                          onClick={() => handleDeleteAddress(index)}
                        />
                      </CambianTooltip>
                    )}
                  </Box>
                </Grid>
                <Grid item xs={5}>
                  <FormControl fullWidth variant="outlined">
                    <TextField
                      value={address.city}
                      onChange={(e) => handleFieldChange(index, 'city', e.target.value)}
                      label={t('City')}
                      size="small"
                      required
                      inputProps={{ readOnly: isClientSummary }}
                      error={!!validationData?.contact?.addresses[index]?.city}
                      helperText={validationData?.contact?.addresses[index]?.city}
                    />
                  </FormControl>
                </Grid>
                <Grid item xs={5}>
                  <FormControl fullWidth variant="outlined">
                    <TextField
                      value={address.province}
                      onChange={(e) => handleFieldChange(index, 'province', e.target.value)}
                      label={t('Province')}
                      size="small"
                      required
                      inputProps={{ readOnly: isClientSummary }}
                      error={!!validationData?.contact?.addresses[index]?.province}
                      helperText={validationData?.contact?.addresses[index]?.province}
                    />
                  </FormControl>
                </Grid>
                <Grid item xs={5}>
                  <FormControl fullWidth variant="outlined">
                    <TextField
                      value={address.country}
                      onChange={(e) => handleFieldChange(index, 'country', e.target.value)}
                      label={t('Country')}
                      size="small"
                      required
                      inputProps={{ readOnly: isClientSummary }}
                      error={!!validationData?.contact?.addresses[index]?.country}
                      helperText={validationData?.contact?.addresses[index]?.country}
                    />
                  </FormControl>
                </Grid>
                <Grid item xs={5}>
                  <FormControl fullWidth variant="outlined">
                    <TextField
                      value={address.postalCode}
                      onChange={(e) => handleFieldChange(index, 'postalCode', e.target.value)}
                      label={t('Postal Code')}
                      size="small"
                      required
                      inputProps={{ readOnly: isClientSummary }}
                      error={!!validationData?.contact?.addresses[index]?.postalCode}
                      helperText={validationData?.contact?.addresses[index]?.postalCode}
                    />
                  </FormControl>
                </Grid>
              </Grid>
            </Box>
          </Box>
        ))}
        <Button
          variant="text"
          startIcon={<Add />}
          onClick={handleAddAddress}
          sx={{
            p: '15px 1px',
            cursor: 'pointer',
            fontSize: '15px',
            '&:hover': { backgroundColor: 'transparent', color: '#23527c' },
          }}
        >
          {t('Add Address')}
        </Button>
      </BorderedSection>
    </Grid>
  );
};
