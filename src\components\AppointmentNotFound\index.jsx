import React from 'react';
import { Paper, Grid, Button, Stack, Typography, Box } from '@mui/material';
import { useTranslation } from 'react-i18next';

export const AppointmentNotFound = (props) => {
  const { appointmentNotFoundData } = props;
  const { t } = useTranslation();

  return (
    <Box sx={{ px: '4%', pt: '8px', pb: '48px' }}>
      <Box sx={{ pt: 3, height: '100%' }}>
        <Grid container>
          <Grid item xs={12} sx={{ mb: 2 }}>
            <Typography variant="h5">
              {appointmentNotFoundData?.heading || t('appointmentNotFound')}
            </Typography>
          </Grid>
          <Grid item xs={12} justifyContent="center" alignItems="center">
            <Grid sx={{ minHeight: '300px' }}>
              <Stack>
                <Typography
                  variant="body"
                  dangerouslySetInnerHTML={{
                    __html: appointmentNotFoundData?.description || t('appointmentNotFoundDescription'),
                  }}
                ></Typography>
              </Stack>
            </Grid>
          </Grid>
        </Grid>
      </Box>
    </Box>
  );
};
