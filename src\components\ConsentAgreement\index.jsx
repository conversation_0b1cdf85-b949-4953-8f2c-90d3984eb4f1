import { Checkbox, FormControlLabel, Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { useCommonAPIs } from '@/hooks/useCommonAPIs';
import { useSelector } from 'react-redux';
import { useEffect } from 'react';
import useNotification from '@/hooks/useNotification';
import { Loader } from '..';
import { getParamFromUrl } from '@/containers/commonUtility';

let isFetchConsentCalled = false;

export const ConsentAgreement = (props) => {
  const { t } = useTranslation();
  const [, sendNotification] = useNotification();
  const { title = t('agreeToConsent'), isConsented, handleConsentChange } = props;
  const { fetchConsentAgreementDocument } = useCommonAPIs();
  const { isConsentAgreementFetching, consentAgreementSuccessData, consentAgreementErrorData } = useSelector(
    (state) => state.consentAgreementReducer,
  );

  useEffect(() => {
    if (!isFetchConsentCalled) {
      fetchConsentAgreementDocument();
      isFetchConsentCalled = true;
    }
  }, [consentAgreementSuccessData]);

  // useEffect(() => {
  //   if (consentAgreementErrorData) {
  //     sendNotification({ msg: t('consentAgreementError'), variant: 'error' });
  //   }
  // }, [consentAgreementErrorData]);

  return (
    <>
      <Loader active={isConsentAgreementFetching} />
      <FormControlLabel
        sx={{
          '&.Mui-disabled': {
            cursor: 'not-allowed',
            pointerEvents: 'all !important',
          },
        }}
        control={<Checkbox checked={isConsented} onChange={handleConsentChange} />}
        label={
          <Typography variant="caption">
            {title}{' '}
            {!consentAgreementErrorData?.message ? (
              <a href={consentAgreementSuccessData?.consentAgreementUrl} target="_blank" style={{ color: '#4D76A9' }}>
                {t('consentAgreement')}
              </a>
            ) : (
              <span>{t('consentAgreement')}</span>
            )}
          </Typography>
        }
      />
    </>
  );
};
