NEXT_PUBLIC_APP_ENV=dev

NEXT_PUBLIC_BASE_URL=https://widget.dev.cambianservices.ca

# Google Map API key
NEXT_PUBLIC_GOOGLE_MAP_API_KEY=

# * APIs variables
# widgetconfig api
NEXT_PUBLIC_WIDGET_SERVICES_BASE_URL=https://dev.cambianservices.ca/widget-config

NEXT_PUBLIC_ORGANIZATION_DATA_BASE_URL=https://dev.cambianservices.ca/org-data

# ORG Request
NEXT_PUBLIC_ORGANIZATION_REQUEST_BASE_URL=https://dev.cambianservices.ca/org-requests
# Schedular Booking API Gateway
NEXT_PUBLIC_BOOKING_SERVICES_BASE_URL=https://dev.cambianservices.ca/scheduler-booking

# Artifact repository 
# Private Artifact repository
NEXT_PUBLIC_ARTIFACT_PRIVATE_REPOSITORY_BASE_URL=https://dev.cambianservices.ca/org-artifact-repo

# Public Artifact repository
NEXT_PUBLIC_ARTIFACT_PUBLIC_REPOSITORY_BASE_URL=https://dev.cambianservices.ca/net-artifact-repo

# CDRs
NEXT_PUBLIC_INDIVIDUAL_CDR_BASE_URL=https://dev.cambianservices.ca/ind-cdr

NEXT_PUBLIC_ORGANIZATION_CDR_BASE_URL=https://dev.cambianservices.ca/org-pre-cdr

# Indexes
NEXT_PUBLIC_CONNECTION_INDEX_BASE_URL=https://dev.cambianservices.ca/connection-index

NEXT_PUBLIC_CLIENT_INDEX_BASE_URL=https://dev.cambianservices.ca/client-index

NEXT_PUBLIC_ORGANIZATION_MESSAGING_SERVICE_BASE_URL=https://dev.cambianservices.ca/org-messaging

NEXT_PUBLIC_ORGANIZATION_INDIVIDUAL_SERVICE_BASE_URL=https://dev.cambianservices.ca/ind-data

NEXT_PUBLIC_PDF_GENERATION_SERVICE_BASE_URL=https://dev.cambianservices.ca/org-doc-gen

NEXT_PUBLIC_AWS_SERVICES_ENVIRONMENT=dev

# Allow access to services in Org Env
COGNITO_ORG_MACHINE_USER_POOL_ID=ca-central-1_q6GxwLIsN
COGNITO_ORG_MACHINE_APP_CLIENT_ID=7facb38c9pbms1pfnma7vqugmn
COGNITO_ORG_MACHINE_USERNAME=1cc31d86659442dc5625f569bd0f8306

# Allow access to services in Network Env
COGNITO_NETWORK_MACHINE_USER_POOL_ID=ca-central-1_rPnRLwrid
COGNITO_NETWORK_MACHINE_APP_CLIENT_ID=37fo91agquv79k0j7tj66pqffd

COGNITO_INDIVIDUAL_MACHINE_USER_POOL_ID=ca-central-1_kQFbifOYL
COGNITO_INDIVIDUAL_MACHINE_APP_CLIENT_ID=1u4ib1n2memof9qcpfmhhi13ot

# Cognito User auth variables
COGNITO_INDIVIDUAL_HUMAN_REGION=ca-central-1
COGNITO_INDIVIUDAL_HUMAN_USER_POOL_ID=ca-central-1_t5XYRZzvT
COGNITO_INDIVIUDAL_HUMAN_APP_CLIENT_ID=1o6f912kklrpao3i289a2qbum

NEXTAUTH_URL=$NEXT_PUBLIC_BASE_URL

NEXTAUTH_SECRET=

COGNITO_ORG_MACHINE_APP_CLIENT_SECRET=
COGNITO_ORG_MACHINE_PASSWORD=

COGNITO_NETWORK_MACHINE_APP_CLIENT_SECRET=
COGNITO_NETWORK_MACHINE_PASSWORD=

COGNITO_INDIVIUDAL_HUMAN_APP_CLIENT_SECRET=

COGNITO_INDIVIDUAL_MACHINE_APP_CLIENT_SECRET=
COGNITO_INDIVIDUAL_MACHINE_CREDENTIALS=

