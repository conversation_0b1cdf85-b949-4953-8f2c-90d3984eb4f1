import React, { useEffect, useState } from 'react';
import { Grid, Box, FormControl, TextField, Select, MenuItem, Button, InputLabel, Stack } from '@mui/material';
import { Add, Star, Close, StarOutline } from '@mui/icons-material';
import { CambianTooltip } from '@/components';

export const HealthcareIdFields = ({
  healthcareIds,
  setHealthcareIds,
  validationData,
  isClientSummary,
  t,
  item,
  handleIndividualChange,
  demographicFields,
  index,
  setShowValidationErrors,
}) => {
  const [currentHealthcareIds, setCurrentHealthcareIds] = useState(
    healthcareIds.length ? healthcareIds : [{ type: '', issuer: '', value: '', primary: true }],
  );
  const primaryHealthcareIdIndex = healthcareIds.length ? healthcareIds.findIndex((id) => id.primary === true) : 0;

  useEffect(() => {
    if (healthcareIds.length > 0) {
      setCurrentHealthcareIds(healthcareIds);
    }
  }, [healthcareIds]);

  if (!demographicFields.individuals[index].healthCareIds) {
    demographicFields.individuals[index].healthCareIds = [];
  }

  const handleFieldChange = (idIndex, field, value) => {
    const updatedHealthcareIds = [...currentHealthcareIds];
    updatedHealthcareIds[idIndex][field] = value;
    if (field === 'type' || field === 'issuer') {
      updatedHealthcareIds[idIndex].value = '';
    }

    setCurrentHealthcareIds(updatedHealthcareIds);
    setHealthcareIds(updatedHealthcareIds);
    demographicFields.individuals[index].healthCareIds = updatedHealthcareIds;
    if (field === 'value' && setShowValidationErrors) {
      setShowValidationErrors(true);
    }

    handleIndividualChange(updatedHealthcareIds, 'healthCareIds', item.isMandatory);
  };

  const handleAddHealthcareId = () => {
    const newHealthcareId = { type: '', issuer: '', value: '', primary: currentHealthcareIds.length === 0 };
    const updatedIds = [...currentHealthcareIds, newHealthcareId];

    setCurrentHealthcareIds(updatedIds);
    setHealthcareIds(updatedIds);
    demographicFields.individuals[index].healthCareIds = updatedIds;
    handleIndividualChange(updatedIds, 'healthCareIds', item.isMandatory);
  };

  const handleSetPrimaryHealthcareId = (idIndex) => {
    const updatedHealthcareIds = currentHealthcareIds.map((id, i) => ({
      ...id,
      primary: i === idIndex,
    }));

    setCurrentHealthcareIds(updatedHealthcareIds);
    setHealthcareIds(updatedHealthcareIds);
    demographicFields.individuals[index].healthCareIds = updatedHealthcareIds;
    handleIndividualChange(updatedHealthcareIds, 'healthCareIds', item.isMandatory);
  };

  const handleDeleteHealthcareId = (idIndex) => {
    const updatedHealthcareIds = currentHealthcareIds.filter((_, i) => i !== idIndex);
    if (!updatedHealthcareIds.some((id) => id.primary) && updatedHealthcareIds.length > 0) {
      updatedHealthcareIds[0].primary = true;
    }

    setCurrentHealthcareIds(updatedHealthcareIds);
    setHealthcareIds(updatedHealthcareIds);
    demographicFields.individuals[index].healthCareIds = updatedHealthcareIds;
    if (validationData?.individuals?.[index]?.healthCareIds) {
      validationData.individuals[index].healthCareIds.splice(idIndex, 1);
    }
    handleIndividualChange(updatedHealthcareIds, 'healthCareIds', item.isMandatory);
  };

  if (!item.allowMultiple) {
    return (
      <Grid item xs={12} sm={10} md={6} lg={5} sx={{ mt: 1, pl: { xs: 1, sm: 3 }, pr: 1 }}>
        <Box sx={{ mt: 2, maxWidth: '500px' }}>
          <Grid container spacing={2} sx={{ flexGrow: 1 }}>
            <Grid item xs={3}>
              <FormControl fullWidth variant="outlined">
                <InputLabel
                  size="small"
                  inputProps={{
                    readOnly: isClientSummary,
                  }}
                  required={item.isMandatory}
                >
                  {t('Type')}
                </InputLabel>
                <Select
                  required={item.isMandatory}
                  value={currentHealthcareIds[primaryHealthcareIdIndex]?.type}
                  onChange={(e) => {
                    let updatedId = {
                      ...(currentHealthcareIds?.[primaryHealthcareIdIndex] || {}),
                      type: e.target.value,
                      issuer: '',
                      value: '',
                      primary: true,
                    };
                    let updatedHealthcareIds = [...currentHealthcareIds];
                    updatedHealthcareIds[primaryHealthcareIdIndex] = updatedId;
                    setCurrentHealthcareIds(updatedHealthcareIds);
                    setHealthcareIds(updatedHealthcareIds);
                    demographicFields.individuals[index].healthCareIds = updatedHealthcareIds;
                  }}
                  label={t('Type')}
                  size="small"
                  inputProps={{
                    readOnly: isClientSummary,
                  }}
                >
                  {currentHealthcareIds[primaryHealthcareIdIndex]?.type && (
                    <MenuItem value="">
                      <em>{t('none')}</em>
                    </MenuItem>
                  )}
                  {item?.idTypes?.map((type, idx) => (
                    <MenuItem key={idx} value={type.idType}>
                      {type.idType}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={4}>
              <FormControl fullWidth variant="outlined">
                <InputLabel
                  size="small"
                  inputProps={{
                    readOnly: isClientSummary,
                  }}
                  required={item.isMandatory}
                  disabled={!currentHealthcareIds[primaryHealthcareIdIndex]?.type}
                >
                  {t('Issuer')}
                </InputLabel>
                <Select
                  required={item.isMandatory}
                  value={currentHealthcareIds[primaryHealthcareIdIndex]?.issuer}
                  onChange={(e) => {
                    let updatedId = { ...currentHealthcareIds[primaryHealthcareIdIndex], issuer: e.target.value };

                    let updatedHealthcareIds = [...currentHealthcareIds];
                    updatedHealthcareIds[primaryHealthcareIdIndex] = updatedId;
                    setCurrentHealthcareIds(updatedHealthcareIds);
                    setHealthcareIds(updatedHealthcareIds);
                    demographicFields.individuals[index].healthCareIds = updatedHealthcareIds;
                  }}
                  size="small"
                  inputProps={{
                    readOnly: isClientSummary,
                  }}
                  label={t('Issuer')}
                  disabled={!currentHealthcareIds[primaryHealthcareIdIndex]?.type}
                  renderValue={(selected) => selected}
                >
                  {currentHealthcareIds[primaryHealthcareIdIndex]?.issuer && (
                    <MenuItem value="">
                      <em>{t('none')}</em>
                    </MenuItem>
                  )}
                  {item?.idTypes
                    ?.find((type) => type.idType === currentHealthcareIds[primaryHealthcareIdIndex]?.type)
                    ?.issuers.map((issuer, idx) => (
                      <MenuItem key={idx} value={issuer.issuer}>
                        {issuer.displayName || issuer.issuer}
                      </MenuItem>
                    ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={5}>
              <FormControl fullWidth variant="outlined">
                <TextField
                  required={item.isMandatory}
                  value={currentHealthcareIds[primaryHealthcareIdIndex]?.value}
                  onChange={(e) => {
                    if (setShowValidationErrors && e.target.value.length > 0) {
                      setShowValidationErrors(true);
                    }

                    let updatedId = { ...currentHealthcareIds[primaryHealthcareIdIndex], value: e.target.value };

                    let updatedHealthcareIds = [...currentHealthcareIds];
                    updatedHealthcareIds[primaryHealthcareIdIndex] = updatedId;
                    setCurrentHealthcareIds(updatedHealthcareIds);
                    setHealthcareIds(updatedHealthcareIds);
                    demographicFields.individuals[index].healthCareIds = updatedHealthcareIds;

                    handleIndividualChange(
                      demographicFields.individuals[index].healthCareIds,
                      'healthCareIds',
                      item.isMandatory,
                    );
                  }}
                  label={t('ID')}
                  size="small"
                  inputProps={{
                    readOnly: isClientSummary,
                  }}
                  disabled={!currentHealthcareIds[primaryHealthcareIdIndex]?.issuer}
                  error={
                    validationData?.individuals
                      ? !!validationData?.individuals[index]?.healthCareIds[primaryHealthcareIdIndex]
                      : false
                  }
                  helperText={
                    validationData?.individuals
                      ? validationData?.individuals[index]?.healthCareIds[primaryHealthcareIdIndex]
                      : ''
                  }
                />
              </FormControl>
            </Grid>
          </Grid>
        </Box>
      </Grid>
    );
  }
  return (
    <Grid item xs={12} sm={10} md={6} lg={5} sx={{ mt: 1, pl: { xs: 1, sm: 3 }, pr: 1 }}>
      {currentHealthcareIds.map((hcId, idx) => (
        <Box key={`healthcare-id-${idx}`} sx={{ mt: 2, maxWidth: '500px' }}>
          <Grid container spacing={1} alignItems="flex-start">
            <Grid item xs={3}>
              <FormControl fullWidth variant="outlined">
                <InputLabel size="small" required={item.isMandatory}>
                  {t('Type')}
                </InputLabel>
                <Select
                  required={item.isMandatory}
                  value={hcId.type}
                  onChange={(e) => handleFieldChange(idx, 'type', e.target.value)}
                  label={t('Type')}
                  size="small"
                  inputProps={{ readOnly: isClientSummary }}
                >
                  {hcId.type && (
                    <MenuItem value="">
                      <em>{t('none')}</em>
                    </MenuItem>
                  )}
                  {item?.idTypes?.map((type, idx) => (
                    <MenuItem key={idx} value={type.idType}>
                      {type.idType}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={3}>
              <FormControl fullWidth variant="outlined">
                <InputLabel size="small" required={item.isMandatory} disabled={!hcId.type}>
                  {t('Issuer')}
                </InputLabel>
                <Select
                  required={item.isMandatory}
                  value={hcId.issuer}
                  onChange={(e) => handleFieldChange(idx, 'issuer', e.target.value)}
                  label={t('Issuer')}
                  size="small"
                  inputProps={{ readOnly: isClientSummary }}
                  disabled={!hcId.type}
                  renderValue={(selected) => selected}
                >
                  {hcId.issuer && (
                    <MenuItem value="">
                      <em>{t('none')}</em>
                    </MenuItem>
                  )}
                  {item?.idTypes
                    ?.find((type) => type.idType === hcId.type)
                    ?.issuers.map((issuer, idx) => (
                      <MenuItem key={idx} value={issuer.issuer}>
                        {issuer.displayName || issuer.issuer}
                      </MenuItem>
                    ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={4}>
              <FormControl fullWidth variant="outlined">
                <TextField
                  required={item.isMandatory}
                  value={hcId.value}
                  onChange={(e) => {
                    if (setShowValidationErrors && e.target.value.length > 0) {
                      setShowValidationErrors(true);
                    }
                    handleFieldChange(idx, 'value', e.target.value);
                  }}
                  label={t('ID')}
                  size="small"
                  inputProps={{ readOnly: isClientSummary }}
                  disabled={!hcId.issuer}
                  error={
                    validationData?.individuals?.[index]?.healthCareIds?.[idx]
                      ? !!validationData.individuals[index].healthCareIds[idx]
                      : false
                  }
                  helperText={
                    validationData?.individuals?.[index]?.healthCareIds?.[idx]
                      ? validationData.individuals[index].healthCareIds[idx]
                      : ''
                  }
                />
              </FormControl>
            </Grid>
            <Grid item xs={2} sx={{ display: 'flex', justifyContent: 'flex-end', alignItems: 'flex-start', pt: 1 }}>
              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'flex-end',
                  alignItems: 'center',
                  height: '40px',
                  position: 'relative',
                  top: '0',
                  zIndex: 1,
                }}
              >
                <Stack direction="row" justifyContent="flex-end" alignItems="center">
                  {hcId.primary ? (
                    <CambianTooltip title={t('Primary')}>
                      <Star color="primary" sx={{ fontSize: '20px' }} />
                    </CambianTooltip>
                  ) : (
                    <CambianTooltip title={t('Set as Primary')}>
                      <StarOutline
                        color="primary"
                        sx={{ cursor: 'pointer', fontSize: '20px' }}
                        onClick={() => handleSetPrimaryHealthcareId(idx)}
                      />
                    </CambianTooltip>
                  )}
                  {currentHealthcareIds?.length > 1 && (
                    <CambianTooltip title={t('Delete ID')}>
                      <Close
                        color="gray"
                        onClick={() => handleDeleteHealthcareId(idx)}
                        sx={{ cursor: 'pointer', ml: 1, fontSize: '20px' }}
                      />
                    </CambianTooltip>
                  )}
                </Stack>
              </Box>
            </Grid>
          </Grid>
        </Box>
      ))}
      <Button
        variant="text"
        startIcon={<Add />}
        onClick={handleAddHealthcareId}
        sx={{
          p: '15px 1px',
          cursor: 'pointer',
          fontSize: '15px',
          '&:hover': { backgroundColor: 'transparent', color: '#23527c' },
        }}
      >
        {t('Add ID')}
      </Button>
    </Grid>
  );
};
