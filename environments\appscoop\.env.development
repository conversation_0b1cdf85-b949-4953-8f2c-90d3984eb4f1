NEXT_PUBLIC_BASE_URL = https://test.d2yc83ebf3vw6y.amplifyapp.com/

# Google Map API key
NEXT_PUBLIC_GOOGLE_MAP_API_KEY=AIzaSyDeW0tEhWQnfETX_YpyZgK-ITlgfVnhFvw


# * APIs variables
NEXT_PUBLIC_WIDGET_SERVICES_BASE_URL=https://y0wjxtcqj7.execute-api.eu-north-1.amazonaws.com/dev

NEXT_PUBLIC_ORGANIZATION_DATA_BASE_URL = https://dev.cambianservices.ca/org-data

# ORG Request
NEXT_PUBLIC_ORGANIZATION_REQUEST_BASE_URL=https://dev.cambianservices.ca/org-requests

# Schedular Booking API Gateway
NEXT_PUBLIC_BOOKING_SERVICES_BASE_URL=https://dev.cambianservices.ca/scheduler-booking

# Artifact repository 
# Private Artifact repository
NEXT_PUBLIC_ARTIFACT_PRIVATE_REPOSITORY_BASE_URL=https://dev.cambianservices.ca/org-artifact-repo

# Public Artifact repository
NEXT_PUBLIC_ARTIFACT_PUBLIC_REPOSITORY_BASE_URL=https://dev.cambianservices.ca/net-artifact-repo

# CDRs
NEXT_PUBLIC_INDIVIDUAL_CDR_BASE_URL=https://dev.cambianservices.ca/ind-cdr

NEXT_PUBLIC_ORGANIZATION_CDR_BASE_URL=https://dev.cambianservices.ca/org-pre-cdr

# Indexes
NEXT_PUBLIC_CONNECTION_INDEX_BASE_URL=https://dev.cambianservices.ca/connection-index

NEXT_PUBLIC_CLIENT_INDEX_BASE_URL=https://dev.cambianservices.ca/client-index

NEXT_PUBLIC_PDF_GENERATION_SERVICE_BASE_URL=https://dev.cambianservices.ca/org-doc-gen

NEXT_PUBLIC_ORGANIZATION_MESSAGING_SERVICE_BASE_URL=https://dev.cambianservices.ca/org-messaging

NEXT_PUBLIC_ORGANIZATION_INDIVIDUAL_SERVICE_BASE_URL=https://dev.cambianservices.ca/ind-data

NEXT_PUBLIC_AWS_SERVICES_ENVIRONMENT=dev

# Allow access to services in Org Env
COGNITO_ORG_MACHINE_USER_POOL_ID=ca-central-1_q6GxwLIsN
COGNITO_ORG_MACHINE_APP_CLIENT_ID=7facb38c9pbms1pfnma7vqugmn
COGNITO_ORG_MACHINE_APP_CLIENT_SECRET=ov5lou5mu8i05sugf9ojb1ebldasn1t6m5l37v7nnrcgtvg142f
COGNITO_ORG_MACHINE_USERNAME=1cc31d86659442dc5625f569bd0f8306
COGNITO_ORG_MACHINE_PASSWORD=LoaVRpO+BTPgYjjtbYeO0mXMr1kb48t9lwRIUgyUMGf=

# Allow access to services in Network Env
COGNITO_NETWORK_MACHINE_USER_POOL_ID=ca-central-1_rPnRLwrid
COGNITO_NETWORK_MACHINE_APP_CLIENT_ID=37fo91agquv79k0j7tj66pqffd
COGNITO_NETWORK_MACHINE_APP_CLIENT_SECRET=eeu584gftupjg5r25pj026ev9b48dimq9ijjd7beh3u0e6co9j1
COGNITO_NETWORK_MACHINE_USERNAME=180d2dc0a7a1172f0ad3a2203617f22d
COGNITO_NETWORK_MACHINE_PASSWORD= FnmBpU4gQdb4G8oxtnrGx2z5nbnixGHbJDETtpH9wCu=

WIDGET_ALLOW_COGNITO_ACCESS_IAM_ACCESS_KEY=******************** #It is navigator IAM as Widget IAM was not given in the sheet
WIDGET_ALLOW_COGNITO_ACCESS_IAM_SECRET_KEY=jJIGlvO5lualKYXQaaCyQew3+39PILWpTU9CCQ5u

# Allow access to services in Individual Env
COGNITO_INDIVIDUAL_MACHINE_USER_POOL_ID=ca-central-1_kQFbifOYL
COGNITO_INDIVIDUAL_MACHINE_APP_CLIENT_ID=1u4ib1n2memof9qcpfmhhi13ot
COGNITO_INDIVIDUAL_MACHINE_APP_CLIENT_SECRET=1getbdgqk0jtclbd37h1qccceiqdr8dopt0dk56m88ji84pccp3n
COGNITO_INDIVIDUAL_MACHINE_USERNAME=cd03026574ac9159b56479bb747b2014
COGNITO_INDIVIDUAL_MACHINE_PASSWORD=6c/TIybA876zLgV5FLLzcp5S2CV2/9sMlhalsmpq2MG=

# Cognito Human auth variables
COGNITO_INDIVIDUAL_HUMAN_REGION=ca-central-1
COGNITO_INDIVIUDAL_HUMAN_USER_POOL_ID=ca-central-1_t5XYRZzvT
COGNITO_INDIVIUDAL_HUMAN_APP_CLIENT_ID=1o6f912kklrpao3i289a2qbum
COGNITO_INDIVIUDAL_HUMAN_APP_CLIENT_SECRET=14ndlopd7e0nd461u1n24kuf9cdis8ab6obqll4o45emjetv3u4t

NEXTAUTH_URL=http://localhost:3050
NEXTAUTH_SECRET=RaB2obLvjFGIAWGLSLHBxDjQjMbpESnOxGeyPEJ6w6I= #same as before
