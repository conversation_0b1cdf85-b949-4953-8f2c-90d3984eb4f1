/* eslint-disable no-self-assign */
/*
 *
 */

import axios from 'axios';
import { RESPONSE_DATA, RESPONSE_MESSAGE, RESPONSE_STATUS, CAMBIAN_PASSCODE, INDIVIDUAL } from '../utils/constants';
import {
  API_HEADER_CONTENT_TYPE,
  API_HEADER_CONTENT_TYPE_JSON_VALUE,
  API_RESPONSE_201_SUCCESS,
  API_RESPONSE_204_SUCCESS,
  API_RESPONSE_AUTHENTICATION_409_ERROR,
  API_RESPONSE_SUCCESS,
  DEFAULT_ERROR_STATUS,
} from '../utils/constants/apiCodes';
import { BASE_URL } from '../utils/constants/apiEndpoints';
import { getMachineAccessToken, getUserTokenAndUpdatedUrl } from './authAPIUtility';
/**
 * unit: milliseconds
 */
const TIMEOUT = 10000;

/**
 * BASE URL
 */

export const SERVER_BASE_URL = BASE_URL;

const commonHeaders = {};

const AxiosServer = axios.create({
  baseURL: SERVER_BASE_URL,
  timeout: TIMEOUT,
});

/**
 *
 * @param config
 */
async function makeAxiosNetworkCall(config) {
  let response = {};
  const defaultErrorString = 'Some thing wrong Error ...';
  commonHeaders[API_HEADER_CONTENT_TYPE] = API_HEADER_CONTENT_TYPE_JSON_VALUE;
  AxiosServer.defaults.headers.common = {};
  try {
    response = await AxiosServer(config);
  } catch (error) {
    if (error.response) {
      response[RESPONSE_MESSAGE] = defaultErrorString;
      response[RESPONSE_STATUS] = DEFAULT_ERROR_STATUS;
    } else {
      // error.message is the 'Network Error' received from Axios
      response[RESPONSE_MESSAGE] = error.message || defaultErrorString;
      response[RESPONSE_STATUS] = DEFAULT_ERROR_STATUS;
    }
  }

  return response;
}

/**
 * Makes a network call with the given configuration.
 *
 * @param {Object} config - The configuration object for the network call.
 * @param {string} config.url - The URL for the API call.
 * @param {string} config.method - The HTTP method (e.g., 'GET', 'POST').
 * @param {Object} [config.headers] - Optional headers for the request.
 * @param {*} [config.data] - The body payload for the request.
 * @param {boolean} [config.formData=false] - Whether the request body is formData.
 * @param {string} config.targetAwsEnv - The target AWS environment (e.g., 'INDIVIDUAL').
 */
export async function makeFetchNetworkCall(config) {
  let response = {};
  const defaultErrorString = 'Some thing wrong in BE server...';
  let url = config.url;
  const { formData = false } = config;
  const headers = {
    ...commonHeaders,
    ...config.headers,
  };

  try {
    const machineAccessToken = await getMachineAccessToken(config.targetAwsEnv);
    headers.Authorization = `Bearer ${machineAccessToken}`;

    if (config.targetAwsEnv === INDIVIDUAL) {
      const [newUrl, userIdToken] = await getUserTokenAndUpdatedUrl(url);
      if (newUrl) {
        url = newUrl;
      }
      // Only adds the token if the individual human user is signed in.
      if (userIdToken) {
        headers['Cambian-User-Token'] = `Bearer ${userIdToken}`;
      }
    }
  } catch (err) {
    console.log(err);
    console.log('Adding machine token or user token failed');
    throw err;
  }

  console.log('HEADERS', headers);
  const params = {
    method: config.method,
    headers: headers,
    body: formData ? config.data : JSON.stringify(config.data),
    timeoutInterval: TIMEOUT,
  };

  const res = await fetch(url, params);

  try {
    if (res) {
      response[RESPONSE_STATUS] = res.status;
      if (
        response[RESPONSE_STATUS] === API_RESPONSE_201_SUCCESS ||
        response[RESPONSE_STATUS] === API_RESPONSE_SUCCESS ||
        response[RESPONSE_STATUS] === API_RESPONSE_204_SUCCESS
      ) {
        if (response[RESPONSE_STATUS] === API_RESPONSE_204_SUCCESS) {
          response[RESPONSE_STATUS] = 200;
          if (res?.headers?.get(CAMBIAN_PASSCODE)) {
            response[RESPONSE_DATA] = {
              CAMBIAN_PASSCODE: res.headers.get(CAMBIAN_PASSCODE),
            };
          }
        } else {
          response[RESPONSE_STATUS] = API_RESPONSE_SUCCESS;
          const resCopy = res.clone();
          response[RESPONSE_DATA] = await res.json().catch((_) => resCopy.text());
        }
      } else if (response[RESPONSE_STATUS] === API_RESPONSE_AUTHENTICATION_409_ERROR) {
        response[RESPONSE_MESSAGE] = res.statusText;
      } else {
        // I  removed hardcoded 500 conditions . this is blocking api json response for status 500.
        response[RESPONSE_DATA] = await res.json();
      }

      if (
        response[RESPONSE_STATUS] !==
        (API_RESPONSE_SUCCESS ||
          API_RESPONSE_201_SUCCESS ||
          API_RESPONSE_204_SUCCESS ||
          API_RESPONSE_AUTHENTICATION_409_ERROR)
      ) {
        const { status = DEFAULT_ERROR_STATUS, statusText = defaultErrorString } = res;
        response[RESPONSE_MESSAGE] = statusText || defaultErrorString;
        response[RESPONSE_STATUS] = status || DEFAULT_ERROR_STATUS;
        if (response) {
          if (response[RESPONSE_DATA]) {
            response[RESPONSE_MESSAGE] = response[RESPONSE_DATA].Message;
            response[RESPONSE_STATUS] = response[RESPONSE_DATA].status || res.status;
            response[RESPONSE_DATA] = response[RESPONSE_DATA];
          } else {
            console.log('error');
          }
        }
      }
    }
  } catch (error) {
    console.log('error', error);
    response[RESPONSE_MESSAGE] = defaultErrorString;
    response[RESPONSE_STATUS] = res.status || DEFAULT_ERROR_STATUS;
  }

  return response;
}
/*
 *
 */

/**
 *
 * @param config
 */
async function makeImageUploadNetworkCall(config) {
  let response = {};
  const defaultErrorString = 'Some thing wrong Error ...';
  const url = SERVER_BASE_URL + config.url;
  // commonHeaders[API_HEADER_CONTENT_TYPE] = API_HEADER_CONTENT_TYPE_JSON_VALUE;

  const machineAccessToken = await getMachineAccessToken(config.targetAwsEnv);

  const headers = {
    ...commonHeaders,
    ...config.headers,
    Authorization: `Bearer ${machineAccessToken}`,
  };

  const params = {
    method: config.method,
    headers: headers,
    body: config.data,
    timeoutInterval: TIMEOUT,
  };

  const res = await fetch(url, params);

  try {
    if (res) {
      response[RESPONSE_STATUS] = res.status;
      if (response[RESPONSE_STATUS] === API_RESPONSE_201_SUCCESS) {
        response[RESPONSE_STATUS] = 200;
        response[RESPONSE_DATA] = await res.json();
      } else {
        response[RESPONSE_DATA] = await res.json();
      }
      if (response[RESPONSE_STATUS] !== (API_RESPONSE_SUCCESS || API_RESPONSE_201_SUCCESS)) {
        response[RESPONSE_MESSAGE] = response[RESPONSE_DATA].Error.Message || defaultErrorString;
        response[RESPONSE_STATUS] = DEFAULT_ERROR_STATUS;
        if (response) {
          if (response[RESPONSE_DATA]) {
            response[RESPONSE_MESSAGE] = response[RESPONSE_DATA].Error.Message;
            response[RESPONSE_STATUS] = response[RESPONSE_DATA].Error.StatusCode || res.status;
            // eslint-disable-next-line no-self-assign
            response[RESPONSE_DATA] = response[RESPONSE_DATA];
          } else {
            response[RESPONSE_MESSAGE] = defaultErrorString;
            response[RESPONSE_STATUS] = DEFAULT_ERROR_STATUS;
          }
        }
      }
    }
  } catch (error) {
    console.log('error', error);
    response[RESPONSE_MESSAGE] = defaultErrorString;
    response[RESPONSE_STATUS] = DEFAULT_ERROR_STATUS;
  }
  return response;
}
/*
 *
 */

function makeNetworkCall(config) {
  return makeFetchNetworkCall(config);
}
// TODO: i dont think this is being used.
function makeImageUploadCall(config) {
  if (config) {
    return makeImageUploadNetworkCall(config);
  } else {
    return makeAxiosNetworkCall(config);
  }
}

export { AxiosServer, makeNetworkCall, makeImageUploadCall };
