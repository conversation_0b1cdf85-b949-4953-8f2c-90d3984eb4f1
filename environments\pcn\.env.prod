# This is for the pcn-prod account

NEXT_PUBLIC_BASE_URL = https://widget.prod.alberta-pcn.ca

# * APIs variables
NEXT_PUBLIC_WIDGET_SERVICES_BASE_URL=https://prod.alberta-pcn.ca/widget-config

# Schedular Booking API Gateway
NEXT_PUBLIC_BOOKING_SERVICES_BASE_URL=https://prod.alberta-pcn.ca/scheduler-booking

# Artifact repository
# Private Artifact Repository
NEXT_PUBLIC_ARTIFACT_PRIVATE_REPOSITORY_BASE_URL=https://prod.alberta-pcn.ca/org-artifact-repo

# Public Artifact Repository
NEXT_PUBLIC_ARTIFACT_PUBLIC_REPOSITORY_BASE_URL=https://prod.cambianservices.ca/net-artifact-repo

# CDRs
NEXT_PUBLIC_INDIVIDUAL_CDR_BASE_URL=https://70e4ltesad.execute-api.ca-central-1.amazonaws.com/prod

NEXT_PUBLIC_ORGANIZATION_CDR_BASE_URL=https://wh54glk4na.execute-api.ca-central-1.amazonaws.com/prod

# Indexes
NEXT_PUBLIC_CONNECTION_INDEX_BASE_URL=https://tt835tu6n3.execute-api.ca-central-1.amazonaws.com/prod

NEXT_PUBLIC_CLIENT_INDEX_BASE_URL=https://48x68y0cpk.execute-api.ca-central-1.amazonaws.com/prod

NEXT_PUBLIC_ORGANIZATION_MESSAGING_SERVICE_BASE_URL=https://prod.alberta-pcn.ca/org-messaging

NEXT_PUBLIC_ORGANIZATION_INDIVIDUAL_SERVICE_BASE_URL=https://fq53ljixj1.execute-api.ca-central-1.amazonaws.com/prod

# Are they necessary?
NEXT_PUBLIC_PRACTITIONER_INDEX_IDENTIFIER=3fd7szhb9f
NEXT_PUBLIC_PRACTITIONER_INDEX_STAGE=prod

NEXT_PUBLIC_LOCATION_INDEX_IDENTIFIER=10zk9zdg9f
NEXT_PUBLIC_LOCATION_INDEX_STAGE=prod

# Doc Gen
NEXT_PUBLIC_PDF_GENERATION_SERVICE_BASE_URL=https://prod.alberta-pcn.ca/org-doc-gen

NEXT_PUBLIC_APPSCOOP_WIDGET_SERVICES_REGION=ca-central-1
NEXT_PUBLIC_CAMBIAN_SERVICES_REGION=ca-central-1

NEXT_PUBLIC_AWS_SERVICES_ENVIRONMENT=prod

# Allow access to services in Org Env
COGNITO_ORG_MACHINE_USER_POOL_ID=ca-central-1_svaf2J18G
COGNITO_ORG_MACHINE_APP_CLIENT_ID=r5ong5l1190b47pdsj7h7e3d0
COGNITO_ORG_MACHINE_USERNAME=e93188ac8ddce2b9d1fbcf9cc23f0957

# Allow access to services in Network Env
COGNITO_NETWORK_MACHINE_USER_POOL_ID=ca-central-1_yUJDsI832
COGNITO_NETWORK_MACHINE_APP_CLIENT_ID=1v4mfl45loagjpaqnb8h6mpm6i
COGNITO_NETWORK_MACHINE_USERNAME=c3c8df1adc25d79d89fa871654c0e1c0

# Allow access to services in Individual Env
COGNITO_INDIVIDUAL_MACHINE_USER_POOL_ID=ca-central-1_kQFbifOYL
COGNITO_INDIVIDUAL_MACHINE_APP_CLIENT_ID=1u4ib1n2memof9qcpfmhhi13ot
COGNITO_INDIVIDUAL_MACHINE_USERNAME=11d302c3ff014aca8873fd9030817291

# Cognito User auth variables
COGNITO_INDIVIDUAL_HUMAN_REGION=ca-central-1
COGNITO_INDIVIUDAL_HUMAN_USER_POOL_ID=ca-central-1_t5XYRZzvT
COGNITO_INDIVIUDAL_HUMAN_APP_CLIENT_ID=1o6f912kklrpao3i289a2qbum

NEXTAUTH_URL=$NEXT_PUBLIC_BASE_URL
