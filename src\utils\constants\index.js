/**
 *
 */

export const GENERIC_ERROR_MSG = 'Something went wrong, please try again';
export const DEV = 'dev';
export const TEST = 'tst';
export const IN_PROGRESS = 'InProgress';
export const BASE64_IMAGE = 'data:image/jpg;base64,';
export const COMPLETED = 'Completed';
export const RESPONSE_STATUS = 'status';
export const RESPONSE_MESSAGE = 'message';
export const RESPONSE_DATA = 'data';
export const REFRESH_TOKEN_INTERVAL = 900000;
export const ORGANIZATION_ID = 'ORGANIZATION_ID';
export const APPOINTMENT_ID = 'APPOINTMENT_ID';
export const WIDGET_ID = 'WIDGET_ID';
export const LOCATION_ID = 'LOCATION_ID';
export const APPOINTMENT_TYPE = 'APPOINTMENT_TYPE';
export const INDIVIDUALS_COUNT = 'INDIVIDUALS_COUNT';
export const ACCESS_TOKEN = 'access_token';
export const DATE_NOW = 'DATE_NOW';
//For production environment use this url
export const CAMBIAN_LOGIN_URL = process.env.REACT_APP_CAMBIAN_LOGIN_URL;
export const CAMBIAN_REGISTRATION_URL = process.env.REACT_APP_CAMBIAN_REGISTRATION_URL;
export const BASIC_TOKEN = 'd2lkZ2V0OnNlY3JldA==';
export const INDIVIDUAL_USER_BASIC_TOKEN = 'Y2FtYmlhbi1wYXNzOnNlY3JldA==';
export const TOKEN = '2e624496-08d8-4750-9f68-2ec63e5d81d9';
export const GENDER = 'Gender';
export const AMPM = 'AMPM';
export const MALE = 'Male';
export const FEMALE = 'Female';
export const UNKNOWN = 'Unknown';
export const TIME_SLOT_MINUTES = 15;
export const TIME_FORMAT = 'hh:mm A';
export const DATE_FORMAT = 'dddd, MMMM D, YYYY';
export const TIME_FORMAT_SMALL_CASE = 'h:mm a';
export const TIME_FORMAT_LONG = 'dddd, D MMM YYYY h:mm a'; //Monday, 30 Nov 2021 2:00 pm (PST)
export const TIME_ZONE = 'PST';
export const SUCCESS = 'SUCCESS';
export const FAILURE = 'Error! ';
export const RATING_NUMBER = 10;
export const PATIENT_ID = 'PATIENT_ID';
export const SERVICE_ID = 'SERVICE_ID';
export const QUESTIONNAIRE_ID = 'QUESTIONNAIRE_ID';
export const EMAIL_VERIFICATION_REQUIRED = 'EMAIL_VERIFICATION_REQUIRED';
export const SEARCH_PATIENTS = 'SEARCH_PATIENTS';
export const BIRTH_DATE = 'BIRTH_DATE';
export const FAMILY = 'FAMILY';
export const GIVEN = 'GIVEN';
export const EMAIL = 'EMAIL';
export const MATCHANY = 'MATCHANY';
export const PHN = 'PHN';
export const UNIQUEKEY = 'UNIQUEKEY';
export const QUESTIONNAIRE_RESPONSE_ID = 'QUESTIONNAIRE_RESPONSE_ID';
export const START_TIME = 'START_TIME';
export const CLIENT_GROUP_ID = process.env.REACT_APP_CLIENT_GROUP_ID;
export const CAMBIAN_PASSCODE = 'Cambian-Passcode';
export const APPOINTMENT_TYPE_STRING = 'appointment-type';
export const ORGANIZATION = 'organization';
export const LOCATION_IDS = 'location-ids';
export const CODE = 'code';
export const FINALIZE = 'FINALIZE';
export const DOWNLOAD_REPORT_ID = 'DOWNLOAD_REPORT_ID';
export const CURRENT_URL = 'current_url';

// *New Constants (v2)
export const API_IDENTIFIER = 'API_IDENTIFIER';
export const REGION = 'REGION';
export const API_ENVIRONMENT = 'API_ENVIRONMENT';
export const REGISTRY = 'REGISTRY';
export const INDIVIDUAL_ID = 'INDIVIDUAL_ID';
export const INCLUDE_SHARE_ATTRIBUTES = 'INCLUDE_SHARE_ATTRIBUTES';
export const UNVERIFIED_USER_EMAIL = 'unverified-user-email';
export const UNVERIFIED_USER_DETAILS = 'unverified-user-details';
export const FIRST_NAME_COGNITO_ATTRIBUTE_NAME = 'given_name';
export const LAST_NAME_COGNITO_ATTRIBUTE_NAME = 'family_name';
export const USER_NOT_CONFIRMED_EXCEPTION = 'UserNotConfirmedException';
export const REQUEST_ID = 'REQUEST_ID';

// Machine auth related var
export const NETWORK = 'network';
export const INDIVIDUAL = 'individual';
export const MACHINE_ACCESS_TOKEN = (env) => `${env}-machine-access-token`;
export const COUNTRY_CODE_ONLY_PATTERN = /^\+(\d{0,3})?$/;
