import { useDispatch, useSelector } from 'react-redux';
import {
  generateAPIHeaderForCambianUserInfoEndPoint,
  generateAPIHeaderWithoutAccessToken,
} from '@/utils/constants/common';
import { getOrganizationAndWidgetId } from '../commonUtility';
import { generateHtmlReportAsPdf } from '@/redux/actions/generateHtmlReportAsPdf';
import { getReportDataForPdf } from '@/redux/actions/getReportDataForPdf';
import { saveQuestionnaireResponseIndividual } from '@/redux/actions/saveQuestionnaireResponseIndividual';
import { saveQuestionnaireResponseOrganization } from '@/redux/actions/saveQuestionnaireResponseOrganization';
import { useCommonAPIs } from '@/hooks/useCommonAPIs';
import { useSession } from 'next-auth/react';
import { getPdfInfo } from '@/redux/actions/getPdfReport';
import { fetchQuestionnaireQuestions } from '@/redux/actions/getQuestionnaireDetails';
import {
  GET_ORGANIZATION_DETAILS,
  GET_REPORT_SETTINGS,
  DOWNLOAD_HTML_REPORT_AS_PDF,
} from '@/utils/constants/awsApiEndpoints';
import { ORGANIZATION_ID } from '@/utils/constants';
import { makeFetchNetworkCall } from '@/network';

export const useAPIs = () => {
  const dispatch = useDispatch();
  const { data: session } = useSession();

  const { questionnaireQuestionsSuccessData } = useSelector((state) => state.getQuestionnaireQuestionRedux);

  const { getClientId } = useCommonAPIs();
  const [organizationId] = getOrganizationAndWidgetId();
  const { individualUserOAuthSuccessData } = useSelector((state) => state.individualUserOAuthReducer);
  const { individualUserInfoSuccessData } = useSelector((state) => state.individualUserInfoReducer);

  const headersWithoutAccessToken = generateAPIHeaderWithoutAccessToken();
  let headersWithAccessToken = generateAPIHeaderForCambianUserInfoEndPoint(individualUserOAuthSuccessData?.accessToken);
  headersWithAccessToken['Content-type'] = 'application/json';

  const saveQuestionnaireResponseAtIndividual = (response) => {
    dispatch(
      saveQuestionnaireResponseIndividual({
        headers: { Authorization: session?.user?.idToken },
        data: {
          questionnaire: questionnaireQuestionsSuccessData?.questionnaire,
          questionnaireResponse: response,
        },
        individualId: session?.user?.cambianId,
      }),
    );
  };

  const saveQuestionnaireResponseAtOrganization = (response) => {
    const individualId = getClientId();
    dispatch(
      saveQuestionnaireResponseOrganization({
        data: response,
        organizationId,
      }),
    );
  };

  const fetchGenerateHtmlReportAsPdf = (data) => {
    dispatch(generateHtmlReportAsPdf({ headers: headersWithoutAccessToken, data }));
  };

  const fetchGetReportDataForPdf = (questionnaireReportById) => {
    dispatch(getReportDataForPdf({ questionnaireReportById }));
  };

  const fetchGeneratePdfReport = (pdfReportData) => {
    dispatch(getPdfInfo({ pdfReportData }));
  };

  const fetchQuestionnaire = (questionnaireId, repository) => {
    dispatch(
      fetchQuestionnaireQuestions({
        questionnaireId,
        repository,
      }),
    );
  };

  const fetchOrganizationDetails = async () => {
    const [organizationId] = getOrganizationAndWidgetId();
    const url = GET_ORGANIZATION_DETAILS.replace(ORGANIZATION_ID, organizationId);

    const response = await makeFetchNetworkCall({ url, method: 'GET' });
    return response;
  };

  const fetchReportSettings = async () => {
    const url = GET_REPORT_SETTINGS.replace(ORGANIZATION_ID, organizationId);

    const response = await makeFetchNetworkCall({ url, method: 'GET' });
    return response;
  };

  const fetchDownloadHtmlReport = async (appUrl, htmlString) => {
    const url = DOWNLOAD_HTML_REPORT_AS_PDF;
    const response = await makeFetchNetworkCall({
      url,
      method: 'POST',
      formData: true,
      data: JSON.stringify({
        htmlString,
      }),
    });
    return response;
  };

  return {
    fetchGenerateHtmlReportAsPdf,
    fetchGetReportDataForPdf,
    saveQuestionnaireResponseAtIndividual,
    saveQuestionnaireResponseAtOrganization,
    fetchGeneratePdfReport,
    fetchQuestionnaire,
    fetchOrganizationDetails,
    fetchReportSettings,
    fetchDownloadHtmlReport,
  };
};
