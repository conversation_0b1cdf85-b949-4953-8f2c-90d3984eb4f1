/* eslint-disable no-useless-escape */
import i18n from '../../../i18n';

const getValidationData = () => ({
  firstName: {
    valid: {
      regex: new RegExp(/^[a-zA-Z .-]*$/),
      message: i18n.t('firstNameValid'),
    },
    required: {
      regex: new RegExp(/\S+/),
      message: i18n.t('requiredField'),
    },
  },
  middleName: {
    valid: {
      regex: new RegExp(/^[a-zA-Z .-]*$/),
      message: i18n.t('middleNameValid'),
    },
    required: {
      regex: new RegExp(/\S+/),
      message: i18n.t('requiredField'),
    },
  },
  lastName: {
    valid: {
      regex: new RegExp(/^[a-zA-Z .-]*$/),
      message: i18n.t('lastNameValid'),
    },
    required: {
      regex: new RegExp(/\S+/),
      message: i18n.t('requiredField'),
    },
  },
  phoneNumbers: {
    valid: {
      regex: new RegExp(/^\+\d{1,3}\s+[\d\s()-]*(\d[\d\s()-]*){10}$/),
      message: i18n.t('phoneNumberValid'),
    },
    required: {
      regex: new RegExp(/\S+/),
      message: i18n.t('requiredField'),
    },
    duplicate: {
      message: i18n.t('phoneNumberDuplicate'),
    },
  },
  emailAddresses: {
    valid: {
      regex: new RegExp(
        /^(([^<>()[\]\.,;:\s@\"]+(\.[^<>()[\]\.,;:\s@\"]+)*)|(\".+\"))@(([^<>()[\]\.,;:\s@\"]+\.)+[^<>()[\]\.,;:\s@\"]{2,})$/i,
      ),
      message: i18n.t('emailValidationText'),
    },
    required: {
      regex: new RegExp(/\S+/),
      message: i18n.t('requiredField'),
    },
    duplicate: {
      message: i18n.t('emailDuplicate'),
    },
  },
  subscribeToNotifications: {
    required: {
      regex: new RegExp(/\S+/),
      message: i18n.t('requiredField'),
    },
  },
  dateOfBirth: {
    required: {
      regex: new RegExp(/\S+/),
      message: i18n.t('requiredField'),
    },
  },
  gender: {
    required: {
      regex: new RegExp(/\S+/),
      message: i18n.t('requiredField'),
    },
  },
  phnType: {
    required: {
      regex: new RegExp(/\S+/),
      message: i18n.t('requiredField'),
    },
  },
  healthCareIds: {
    required: {
      regex: new RegExp(/\S+/),
      message: i18n.t('requiredField'),
    },
    minLength: {
      regex: new RegExp(/.{2,}/),
      message: i18n.t('idMinLength'),
    },
    bcPHN: {
      regex: new RegExp(/^\d{10}$/),
      message: i18n.t('bcPersonalHealthNumberMin'),
    },
    ytPHN: {
      regex: new RegExp(/^00\d{7}$/),
      message: i18n.t('ytPersonalHealthNumberMin'),
    },
  },
  addresses: {
    min: {
      regex: new RegExp(/[a-zA-Z]{2,}/),
      message: i18n.t('addressValidationMin'),
    },
    required: {
      regex: new RegExp(/\S+/),
      message: i18n.t('requiredField'),
    },
  },
  address1: {
    min: {
      regex: new RegExp(/[a-zA-Z]{2,}/),
      message: i18n.t('cityValidationMin'),
    },
    required: {
      regex: new RegExp(/\S+/),
      message: i18n.t('requiredField'),
    },
  },
  address2: {
    min: {
      regex: new RegExp(/[a-zA-Z]{2,}/),
      message: i18n.t('cityValidationMin'),
    },
  },
  city: {
    min: {
      regex: new RegExp(/[a-zA-Z]{2,}/),
      message: i18n.t('cityValidationMin'),
    },
    required: {
      regex: new RegExp(/\S+/),
      message: i18n.t('requiredField'),
    },
  },
  province: {
    min: {
      regex: new RegExp(/[a-zA-Z]{2,}/),
      message: i18n.t('provinceValidationMin'),
    },
    required: {
      regex: new RegExp(/\S+/),
      message: i18n.t('requiredField'),
    },
  },
  country: {
    min: {
      regex: new RegExp(/[a-zA-Z]{2,}/),
      message: i18n.t('countryValidationMin'),
    },
    required: {
      regex: new RegExp(/\S+/),
      message: i18n.t('requiredField'),
    },
  },
  postalCode: {
    min: {
      regex: new RegExp(/^[a-zA-Z0-9\s]{6,}$/),
      message: i18n.t('postalCodeValidationMin'),
    },
    required: {
      regex: new RegExp(/\S+/),
      message: i18n.t('requiredField'),
    },
  },
  preferredContactMethod: {
    required: {
      regex: new RegExp(/\S+/),
      message: i18n.t('requiredField'),
    },
  },
});

let errorMessages = {
  preferredContactMethod: '',
  subscribeToNotifications: '',
  individuals: [
    {
      firstName: '',
      middleName: '',
      lastName: '',
      healthCareIds: '',
      dateOfBirth: '',
      gender: '',
    },
  ],
  contact: {
    addresses: [
      {
        address1: '',
        city: '',
        province: '',
        country: '',
        postalCode: '',
      },
    ],
    emailAddresses: [],
    phoneNumbers: [],
  },
};

export const addIndividualArrayField = () => {
  errorMessages.individuals.push({
    firstName: '',
    middleName: '',
    lastName: '',
    healthCareIds: [],
    dateOfBirth: '',
    gender: '',
  });
};

export const removeIndividualArrayField = (index) => {
  errorMessages.individuals.splice(index, 1);
};

export const contactMethodValidation = (fields, value, demographicFields) => {
  const getField = (code) => fields.find((field) => field.code === code);
  const { contact } = demographicFields;

  const fieldConfigs = {
    PREFERRED_CONTACT_METHOD: getField('PREFERRED_CONTACT_METHOD'),
    PHONE: getField('PHONE'),
    EMAIL: getField('EMAIL'),
  };
  errorMessages.preferredContactMethod = '';
  const shouldValidate = fieldConfigs.PREFERRED_CONTACT_METHOD?.isMandatory || demographicFields.preferredContactMethod;

  if (!shouldValidate) return errorMessages;
  if (!value) {
    if (fieldConfigs.EMAIL && !fieldConfigs.EMAIL.isMandatory) {
      errorMessages.contact.emailAddresses = contact.emailAddresses.map(() => '');
    }
    if (fieldConfigs.PHONE && !fieldConfigs.PHONE.isMandatory) {
      errorMessages.contact.phoneNumbers = contact.phoneNumbers.map(() => '');
    }

    if (fieldConfigs.PREFERRED_CONTACT_METHOD?.isMandatory) {
      errorMessages.preferredContactMethod = getValidationData().preferredContactMethod.required.message;
    }
    return errorMessages;
  }

  const validateContactMethod = {
    Phone: () => {
      const hasEmptyPhone = !contact?.phoneNumbers?.length || !contact.phoneNumbers[0].phoneNumber;
      if (fieldConfigs.PHONE && hasEmptyPhone) {
        errorMessages.contact.phoneNumbers[0] = getValidationData().phoneNumbers.required.message;
      }
      if (fieldConfigs.EMAIL?.isMandatory === false) {
        errorMessages.contact.emailAddresses = contact.emailAddresses.map(() => '');
      }
    },
    Email: () => {
      const hasEmptyEmail = !contact.emailAddresses.length || !contact.emailAddresses[0].emailAddress;
      if (fieldConfigs.EMAIL && hasEmptyEmail) {
        errorMessages.contact.emailAddresses[0] = getValidationData().emailAddresses.required.message;
      }
      if (fieldConfigs.PHONE?.isMandatory === false) {
        errorMessages.contact.phoneNumbers = contact.phoneNumbers.map(() => '');
      }
    },
  };

  validateContactMethod[value]?.();

  const validateAdditionalFields = (items, field, errorKey) => {
    if (items?.length > 1) {
      items.forEach((item, index) => {
        if (!item[field]) {
          errorMessages.contact[errorKey][index] = getValidationData()[errorKey].required.message;
        }
      });
    }
  };

  validateAdditionalFields(contact.phoneNumbers, 'phoneNumber', 'phoneNumbers');
  validateAdditionalFields(contact.emailAddresses, 'emailAddress', 'emailAddresses');

  return errorMessages;
};

export const individualFieldValidation = (value, fieldName, index, isRequired, individualDemographics) => {
  let currentField = getValidationData()[fieldName];

  if (fieldName === 'healthCareIds') {
    errorMessages.individuals[index].healthCareIds = errorMessages.individuals[index].healthCareIds || [];
    if (isRequired && (!value || value?.length === 0)) {
      errorMessages.individuals[index].healthCareIds[0] = getValidationData().healthCareIds.required.message;
    }
    value?.forEach((healthCareId, hcIndex) => {
      errorMessages.individuals[index].healthCareIds[hcIndex] = '';
      if (
        (!isRequired && value?.length > 1 && (!healthCareId.value || healthCareId.value?.length === 0)) ||
        (isRequired && (!healthCareId.value || healthCareId.value?.length === 0))
      ) {
        if ((healthCareId.type && healthCareId.issuer) || (!isRequired && value?.length > 1)) {
          errorMessages.individuals[index].healthCareIds[hcIndex] = currentField.required.message;
        }
        return;
      }
      if (healthCareId.value && healthCareId.value.length > 0) {
        if (!currentField.minLength.regex.test(healthCareId.value)) {
          errorMessages.individuals[index].healthCareIds[hcIndex] = currentField.minLength.message;
          return;
        }
        if (healthCareId.issuer === 'BC' && !currentField.bcPHN.regex.test(healthCareId.value)) {
          errorMessages.individuals[index].healthCareIds[hcIndex] = currentField.bcPHN.message;
          return;
        }
        if (healthCareId.issuer === 'YT' && !currentField.ytPHN.regex.test(healthCareId.value)) {
          errorMessages.individuals[index].healthCareIds[hcIndex] = currentField.ytPHN.message;
          return;
        }
      }
    });
    return errorMessages;
  }
  if (fieldName === 'subscribeToNotifications') {
    if (value) {
      errorMessages.subscribeToNotifications = '';
    } else if (isRequired && value?.length === 0) {
      errorMessages.subscribeToNotifications = currentField.required.message;
    }
    return errorMessages;
  }

  if (isRequired) {
    let valueToValidate = value;
    if (['firstName', 'middleName', 'lastName'].includes(fieldName) && typeof value === 'string') {
      valueToValidate = value.trim();
    }

    if (valueToValidate?.length === 0) {
      errorMessages.individuals[index][fieldName] = currentField.required.message;
    } else if (currentField.valid && !currentField.valid.regex.test(valueToValidate)) {
      errorMessages.individuals[index][fieldName] = currentField.valid.message;
    } else if (currentField.min && !currentField.min.regex.test(valueToValidate)) {
      errorMessages.individuals[index][fieldName] = currentField.min.message;
    } else if (currentField.max && !currentField.max.regex.test(valueToValidate)) {
      errorMessages.individuals[index][fieldName] = currentField.max.message;
    } else {
      errorMessages.individuals[index][fieldName] = '';
    }
  } else if (!isRequired) {
    let valueToValidate = value;
    if (['firstName', 'middleName', 'lastName'].includes(fieldName) && typeof value === 'string') {
      valueToValidate = value.trim();
    }

    if (valueToValidate?.length === 0) {
      errorMessages.individuals[index][fieldName] = '';
    } else if (currentField.valid && !currentField.valid.regex.test(valueToValidate)) {
      errorMessages.individuals[index][fieldName] = currentField.valid.message;
    } else if (currentField.min && !currentField.min.regex.test(valueToValidate)) {
      errorMessages.individuals[index][fieldName] = currentField.min.message;
    } else if (currentField.max && !currentField.max.regex.test(valueToValidate)) {
      errorMessages.individuals[index][fieldName] = currentField.max.message;
    } else {
      errorMessages.individuals[index][fieldName] = '';
    }
  }

  return errorMessages;
};

export const contactFieldValidation = (value, fieldName, isRequired) => {
  const currentField = getValidationData()[fieldName];
  if (fieldName === 'emailAddresses') {
    if (!Array.isArray(errorMessages.contact.emailAddresses)) {
      errorMessages.contact.emailAddresses = [];
    }
    errorMessages.contact.emailAddresses = value.map(() => '');
    if (isRequired && (!value || value?.length === 0)) {
      errorMessages.contact.emailAddresses[0] = getValidationData().emailAddresses.required.message;
    } else {
      const emailIndices = {};
      value.forEach((email, index) => {
        if (email.emailAddress) {
          emailIndices[email.emailAddress] = emailIndices[email.emailAddress] || [];
          emailIndices[email.emailAddress].push(index);
        }
      });

      value?.forEach((email, index) => {
        if (
          (!isRequired && value?.length > 1 && email.emailAddress?.length === 0) ||
          (isRequired && email.emailAddress?.length === 0)
        ) {
          errorMessages.contact.emailAddresses[index] = getValidationData().emailAddresses.required.message;
        } else if (
          !getValidationData().emailAddresses.valid.regex.test(email.emailAddress) &&
          email.emailAddress !== ''
        ) {
          errorMessages.contact.emailAddresses[index] = getValidationData().emailAddresses.valid.message;
        } else if (
          email.emailAddress &&
          emailIndices[email.emailAddress].length > 1 &&
          index !== emailIndices[email.emailAddress][0]
        ) {
          errorMessages.contact.emailAddresses[index] = getValidationData().emailAddresses.duplicate.message;
        } else {
          errorMessages.contact.emailAddresses[index] = '';
        }
      });

      return errorMessages;
    }
  }

  if (fieldName === 'phoneNumbers') {
    if (!Array.isArray(errorMessages.contact.phoneNumbers)) {
      errorMessages.contact.phoneNumbers = [];
    }
    errorMessages.contact.phoneNumbers = value.map(() => '');

    if (isRequired && (!value || value?.length === 0)) {
      errorMessages.contact.phoneNumbers[0] = getValidationData().phoneNumbers.required.message;
    } else {
      const phoneIndices = {};
      value.forEach((phone, index) => {
        if (phone.phoneNumber) {
          phoneIndices[phone.phoneNumber] = phoneIndices[phone.phoneNumber] || [];
          phoneIndices[phone.phoneNumber].push(index);
        }
      });

      value?.forEach((phone, index) => {
        if (
          (!isRequired && value?.length > 1 && phone.phoneNumber?.length === 0) ||
          (isRequired && phone.phoneNumber?.length === 0)
        ) {
          errorMessages.contact.phoneNumbers[index] = getValidationData().phoneNumbers.required.message;
        } else if (!getValidationData().phoneNumbers.valid.regex.test(phone.phoneNumber) && phone.phoneNumber !== '') {
          errorMessages.contact.phoneNumbers[index] = getValidationData().phoneNumbers.valid.message;
        } else if (
          phone.phoneNumber &&
          phoneIndices[phone.phoneNumber].length > 1 &&
          index !== phoneIndices[phone.phoneNumber][0]
        ) {
          errorMessages.contact.phoneNumbers[index] = getValidationData().phoneNumbers.duplicate.message;
        } else {
          errorMessages.contact.phoneNumbers[index] = '';
        }
      });

      return errorMessages;
    }
  }

  if (fieldName === 'addresses') {
    errorMessages.contact.addresses = errorMessages.contact.addresses || [];
    if (isRequired && value?.length === 0) {
      errorMessages.contact.addresses[0] = errorMessages.contact.addresses[0] || {};

      ['address1', 'city', 'province', 'country', 'postalCode'].forEach((field) => {
        errorMessages.contact.addresses[0][field] = getValidationData()[field].required.message;
      });

      return errorMessages;
    }

    value?.forEach((address, index) => {
      const hasPartialAddress =
        address.address1 || address.city || address.province || address.country || address.postalCode;

      errorMessages.contact.addresses[index] = errorMessages.contact.addresses[index] || {};

      ['address1', 'city', 'province', 'country', 'postalCode'].forEach((field) => {
        const fieldValidation = getValidationData()[field];

        if (fieldValidation) {
          const shouldShowRequiredError =
            (isRequired && !address[field]) ||
            (!isRequired && value?.length > 1 && !address[field]) ||
            (!isRequired && hasPartialAddress && !address[field] && fieldValidation.required);
          if (shouldShowRequiredError) {
            errorMessages.contact.addresses[index][field] = fieldValidation.required.message;
          } else if (address[field] && fieldValidation.valid && !fieldValidation.valid.regex.test(address[field])) {
            errorMessages.contact.addresses[index][field] = fieldValidation.valid.message;
          } else if (address[field] && fieldValidation.min && !fieldValidation.min.regex.test(address[field])) {
            errorMessages.contact.addresses[index][field] = fieldValidation.min.message;
          } else {
            errorMessages.contact.addresses[index][field] = '';
          }
        }
      });
    });

    return errorMessages;
  }

  return errorMessages;
};

// camelize function is used for changing the name of fields to camelCase so we can make comparison for
// validation of fields with respect to demographicFields
const camelize = (str) => {
  return str
    .replace(/(?:^\w|\[A-Z\]|\b\w)/g, (word, index) => {
      return index === 0 ? word.toLowerCase() : word.toUpperCase();
    })
    .replace(/\s+/g, '');
};

export const validateAllFields = (fields, demographicFields) => {
  const allFields = {
    FIRST_NAME: 'firstName',
    MIDDLE_NAME: 'middleName',
    LAST_NAME: 'lastName',
    DATE_OF_BIRTH: 'dateOfBirth',
    GENDER: 'gender',
    IDENTIFICATION: 'healthCareIds',
    EMAIL: 'emailAddresses',
    PHONE: 'phoneNumbers',
    NOTIFICATIONS: 'subscribeToNotifications',
    ADDRESS: 'addresses',
    PREFERRED_CONTACT_METHOD: 'preferredContactMethod',
  };

  const notMandatoryMultipleFields = fields
    .filter((field) => field.checked && !field.isMandatory)
    .map((field) => allFields[field.code]);

  const mandatoryFields = fields.filter((field) => field.isMandatory).map((field) => allFields[field.code]);

  mandatoryFields.forEach((field) => {
    demographicFields?.individuals.forEach((individual, index) => {
      for (const key in individual) {
        if (key === field) {
          // 4th param is true because all this field are required
          individualFieldValidation(individual[key], key, index, true, individual);
        }
      }
    });
    if (field === 'emailAddresses' || field === 'phoneNumbers' || field === 'addresses') {
      contactFieldValidation(demographicFields.contact[field], field, true);
    } else {
      for (const key in demographicFields.contact) {
        if (key === field) {
          contactFieldValidation(demographicFields.contact[key], key, true);
        }
      }
    }

    for (const key in demographicFields.contact) {
      if (key === field) {
        // 3rd param is true because all this field are required
        contactFieldValidation(demographicFields?.contact[key], key, true);
      }
    }
  });

  notMandatoryMultipleFields.forEach((field) => {
    demographicFields?.individuals.forEach((individual, index) => {
      for (const key in individual) {
        if (key === field) {
          individualFieldValidation(individual[key], key, index, false, individual);
        }
      }
    });
    if (field === 'emailAddresses' || field === 'phoneNumbers' || field === 'addresses') {
      if (demographicFields.contact[field].length > 0) {
        contactFieldValidation(demographicFields.contact[field], field, false);
      }
    }
  });

  if (mandatoryFields.includes('subscribeToNotifications')) {
    const subscribeValue = demographicFields?.subscribeToNotifications;
    if (subscribeValue !== undefined) {
      if (getValidationData().subscribeToNotifications.required && subscribeValue === null) {
        errorMessages.subscribeToNotifications = getValidationData().subscribeToNotifications.required.message;
      } else {
        errorMessages.subscribeToNotifications = '';
      }
    }
  }

  contactMethodValidation(fields, demographicFields?.preferredContactMethod, demographicFields);

  return errorMessages;
};
